{"notify_title": "Banküberfall", "notify_invent_title": "Inventar", "notify_invent_desc": "Inventar voll, erstelle Drop", "laptop_target_label": "Gegenstand anbieten", "laptop_not_enough": "Du hast nicht genug %s", "not_enough_cops": "Nicht genug Polizei, %s erforderlich", "missing_items": "Es fehlen einige Gegenstände..", "canceled": "Abgebrochen..", "locker_hit": "Der Spind ist bereits aufgebrochen..", "minigame_failed": "Minispiel fehlgeschlagen..", "hack_failed": "Hack fehlgeschlagen..", "locker_failed": "Fehler beim Bohren des Spinds..", "thermite_success": "Die Tür ist aufgebrannt!", "thermite_failed": "Thermite fehlgeschlagen..", "bank_hacked": "Du hast das Sicherheitssystem geknackt..", "bank_cooldown": "Die Sicherheitssperre ist aktiv, die Tür zu öffnen ist derzeit nicht möglich..", "locker_missing_drill": "Du brauchst einen Bohrer dafür..", "laptop_hit": "Je<PERSON> hat bereits die Sicherheit dieser Bank gehackt..", "trolly_target_label": "<PERSON><PERSON> holen", "trolly_hit": "<PERSON>s gibt nichts mehr zu holen..", "locker_target_label": "<PERSON><PERSON>", "panel_target_hack": "Hacke Sicherheit", "panel_target_pd": "Tresor sch<PERSON>n", "mail_sender": "h4ckerm4n", "mail_message": "Ich habe deine Eingabecodes erhalten und werde jetzt das Sicherheitssystem deaktivieren..<br/>Das könnte eine Minute dauern..", "progressbar_laptop": "Verbindung zum Laptop herstellen..", "pacific_swipe_card": "Ö<PERSON>ne Sicherheitstür", "pacific_lockdown_active": "Sicherheitssperrung ist aktiv..", "progressbar_swipe_card": "Wische Sicherheitskarte", "pacific_input_header": "Code eingeben", "pacific_code_invalid": "Ungültige Eingabe.. (4 Ziffern)", "pacific_explosive": "Explosion in %s Sekunden!", "input_submit": "<PERSON><PERSON><PERSON><PERSON>", "paleto_lockdown": "Dies wird das Sperrsystem auslösen..", "paleto_lockdown2": "V<PERSON><PERSON><PERSON>t solltest du es zu<PERSON>t deaktivieren?", "paleto_target_sidehack": "Hacke Sicherheitspanel", "pacific_hitbylaser": "<PERSON>, dass der Laser dich berührt..", "vault_flags": "Falsch! (%s/%s)"}