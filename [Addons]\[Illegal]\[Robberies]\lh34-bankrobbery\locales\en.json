{"notify_title": "Bankrobbery", "notify_invent_title": "Inventory", "notify_invent_desc": "Inventory full, creating drop", "laptop_target_label": "Offer <PERSON>em", "laptop_not_enough": "You don't have enough %s", "not_enough_cops": "Not enough cops, %s required", "missing_items": "You are missing some item(s)..", "canceled": "Canceled..", "locker_hit": "The locker is already broken open..", "minigame_failed": "Minigame failed..", "hack_failed": "Hack failed..", "locker_failed": "Failed drilling the locker..", "thermite_success": "The door is burned open!", "thermite_failed": "Thermite failed..", "bank_hacked": "You cracked the security system..", "bank_cooldown": "The security lock is active, opening the door is currently not possible..", "locker_missing_drill": "You need a drill for this..", "laptop_hit": "Somebody already hacked the security of this bank..", "trolly_target_label": "<PERSON><PERSON>", "trolly_hit": "There is nothing to grab anymore..", "locker_target_label": "Open Locker", "panel_target_hack": "Hack Security", "panel_target_pd": "Close <PERSON>ault", "mail_sender": "h4ckerm4n", "mail_message": "I've received your input codes and will now start disabling the security system..<br/>This might take a minute..", "progressbar_laptop": "Connecting the laptop..", "pacific_swipe_card": "Open Security Door", "pacific_lockdown_active": "Security lockdown is active..", "progressbar_swipe_card": "Swiping security card", "pacific_input_header": "Enter Code", "pacific_code_invalid": "Invalid Input.. (4 digits)", "pacific_explosive": "Blast in %s seconds!", "input_submit": "Submit", "paleto_lockdown": "Doing this will trigger the lockdown system..", "paleto_lockdown2": "Maybe you should disable it first?", "paleto_target_sidehack": "Hack Security Panel", "pacific_hitbylaser": "You notice the laser touching you..", "vault_flags": "Incorrect! (%s/%s)"}