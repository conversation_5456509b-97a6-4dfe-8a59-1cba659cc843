{"notify_title": "Asalto al banco", "notify_invent_title": "Inventario", "notify_invent_desc": "Inventario lleno, creando drop", "laptop_target_label": "<PERSON><PERSON><PERSON> objeto", "laptop_not_enough": "No tienes suficiente %s", "not_enough_cops": "No hay suficientes policías, se necesitan %s", "missing_items": "Te faltan algunos objetos...", "canceled": "Cancelado..", "locker_hit": "El casillero ya está abierto..", "minigame_failed": "Minijuego fallido..", "hack_failed": "Hackeo fallido..", "locker_failed": "Fallo al perforar el casillero..", "thermite_success": "¡La puerta está quemada!", "thermite_failed": "Falló la termite..", "bank_hacked": "Has crackeado el sistema de seguridad..", "bank_cooldown": "El bloqueo de seguridad está activo, abrir la puerta no es posible en este momento..", "locker_missing_drill": "Necesitas una taladradora para esto..", "laptop_hit": "Alguien ya ha hackeado la seguridad de este banco..", "trolly_target_label": "Coger botín", "trolly_hit": "Ya no hay nada más para coger..", "locker_target_label": "<PERSON><PERSON><PERSON> ca<PERSON>o", "panel_target_hack": "<PERSON><PERSON><PERSON> seguridad", "panel_target_pd": "<PERSON><PERSON><PERSON>", "mail_sender": "h4ckerm4n", "mail_message": "He recibido tus códigos de entrada y ahora comenzaré a desactivar el sistema de seguridad..<br/>Esto podría tomar un minuto..", "progressbar_laptop": "Conectando el portátil..", "pacific_swipe_card": "<PERSON><PERSON>r puerta de seguridad", "pacific_lockdown_active": "El bloqueo de seguridad está activo..", "progressbar_swipe_card": "<PERSON><PERSON><PERSON> tarjeta de seguridad", "pacific_input_header": "Ingresar código", "pacific_code_invalid": "Entrada inválida.. (4 dígitos)", "pacific_explosive": "¡Explosión en %s segundos!", "input_submit": "Enviar", "paleto_lockdown": "Esto activará el sistema de bloqueo..", "paleto_lockdown2": "¿Quizás deberías desactivarlo primero?", "paleto_target_sidehack": "<PERSON><PERSON><PERSON> panel de seguridad", "pacific_hitbylaser": "Notas que el láser te toca..", "vault_flags": "¡Incorrecto! (%s/%s)"}