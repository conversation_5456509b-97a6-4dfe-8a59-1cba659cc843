local utils = {}

utils.alertPolice = function(bankType)
    if GetResourceState('cd_dispatch') ~= 'started' then return end

    local data = exports['cd_dispatch']:GetPlayerInfo()
    local title, message, sprite, blipText

    if bankType == 'fleeca' then
        title = '10-90 - Fleeca Bank Robbery'
        message = 'A '..data.sex..' robbing Fleeca Bank at '..data.street
        sprite = 431
        blipText = '911 - Fleeca Bank Robbery'
    elseif bankType == 'maze' then
        title = '10-90 - Maze Bank Robbery'
        message = 'A '..data.sex..' robbing Maze Bank at '..data.street
        sprite = 431
        blipText = '911 - Maze Bank Robbery'
    elseif bankType == 'paleto' then
        title = '10-90 - Paleto Bank Robbery'
        message = 'A '..data.sex..' robbing Paleto Bank at '..data.street
        sprite = 431
        blipText = '911 - Paleto Bank Robbery'
    elseif bankType == 'pacific' then
        title = '10-90 - Pacific Bank Robbery'
        message = 'A '..data.sex..' robbing Pacific Bank at '..data.street
        sprite = 431
        blipText = '911 - Pacific Bank Robbery'
    end

    TriggerServerEvent('cd_dispatch:AddNotification', {
        job_table = {'police', 'bcso', 'sasp'},
        coords = data.coords,
        title = title,
        message = message,
        flash = 0,
        unique_id = data.unique_id,
        sound = 1,
        blip = {
            sprite = sprite,
            scale = 2.5,
            colour = 1,
            flashes = false,
            text = blipText,
            time = 5,
            radius = 0,
        }
    })
end

utils.notify = function(message, notifType, timeOut)
    lib.notify({
        title = Locales['notify_title'],
        description = message,
        duration = timeOut,
        type = notifType,
        position = 'center-right',
    })
end

utils.createEvidence = function(coords)
    coords = coords or GetEntityCoords(cache.ped)
    TriggerServerEvent('evidence:server:CreateFingerDrop', coords)
end

return utils
